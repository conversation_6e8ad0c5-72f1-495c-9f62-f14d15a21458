# pc_connect

A Flutter plugin for Windows PC Bluetooth driver detection using native FFI integration.

## Overview

`pc_connect` is a specialized Flutter plugin that provides direct access to Windows Bluetooth hardware driver detection through FFI (Foreign Function Interface) calls to a native DLL. This plugin specifically focuses on low-level Bluetooth driver availability checking.

## Features

- **Native Bluetooth Driver Detection**: Check if Bluetooth hardware drivers are properly installed and working
- **FFI Integration**: Direct native code execution through `dart:ffi`
- **Windows-specific**: Optimized for Windows Bluetooth stack
- **Lightweight**: Minimal overhead with direct system calls

## Architecture

```mermaid
graph TD
    A[Flutter Application] -->|Import| B[pc_connect Plugin]
    B -->|Export| C[BluetoothDriverDetector]
    C -->|dart:ffi| D[bluetooth_detector.dll]
    D -->|Windows API| E[Bluetooth Driver Stack]
    E -->|Hardware Check| F[Bluetooth Hardware]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

## Platform Support

- ✅ **Windows 10/11** (Primary support)
- ❌ Android (Not supported)
- ❌ iOS (Not supported) 
- ❌ macOS (Not supported)
- ❌ Linux (Not supported)

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  pc_connect: ^0.0.1
```

Then run:
```bash
flutter pub get
```

## API Reference

### BluetoothDriverDetector

The main class providing Bluetooth driver detection functionality.

#### Methods

##### `static bool isBluetoothDriverWorking()`

Checks if the Bluetooth driver is properly installed and working.

**Returns:**
- `true` - Bluetooth driver is working correctly
- `false` - Bluetooth driver is not available, not working, or hardware not present

**Example:**
```dart
import 'package:pc_connect/pc_connect.dart';

// Check Bluetooth driver status
bool isDriverWorking = BluetoothDriverDetector.isBluetoothDriverWorking();

if (isDriverWorking) {
  print('Bluetooth driver is working properly');
} else {
  print('Bluetooth driver is not available or not working');
}
```

## Usage Examples

### Basic Driver Check

```dart
import 'package:pc_connect/pc_connect.dart';

  Future<void> _checkBluetoothDriver() async {
    setState(() => _isLoading = true);
    
    try {
      bool isWorking = BluetoothDriverDetector.isBluetoothDriverWorking();
      setState(() {
        _driverStatus = isWorking;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _driverStatus = false;
        _isLoading = false;
      });
      print('Error checking Bluetooth driver: $e');
    }
  }

```

### Error Handling

```dart
import 'package:pc_connect/pc_connect.dart';

Future<void> checkBluetoothWithErrorHandling() async {
  try {
    bool isDriverWorking = BluetoothDriverDetector.isBluetoothDriverWorking();
    
    if (isDriverWorking) {
      print('✅ Bluetooth driver is working properly');
      // Proceed with Bluetooth operations
    } else {
      print('❌ Bluetooth driver is not working');
      // Show user-friendly error message
      // Guide user to check Device Manager
    }
  } catch (e) {
    print('🔥 Exception occurred while checking Bluetooth driver: $e');
    // Handle FFI-related errors
    // Could be DLL loading issues, permission problems, etc.
  }
}
```

## Technical Details

### FFI Integration

This plugin uses Dart's Foreign Function Interface (FFI) to directly call native Windows functions:

```dart
// Internal FFI function signatures (for reference)
typedef IsBluetoothDriverWorkingC = Bool Function();
typedef IsBluetoothDriverWorkingDart = bool Function();
```

### Native DLL

The plugin includes `bluetooth_detector.dll` which:
- Interfaces with Windows Bluetooth APIs
- Performs low-level driver availability checks
- Returns boolean status to Dart code

### File Structure

```
pc_connect/
├── lib/
│   ├── pc_connect.dart                 # Main export file
│   └── bluetooth_driver_detector.dart  # Core FFI implementation
├── windows/
│   └── runner/
│       └── bluetooth_detector.dll      # Native Windows library
└── example/
    └── lib/
        └── main.dart                    # Usage example
```

## Requirements

### System Requirements
- **Operating System**: Windows 10 version 1809 or later
- **Flutter SDK**: ≥ 3.3.0
- **Dart SDK**: ≥ 3.5.0

### Hardware Requirements
- Bluetooth-capable hardware (for meaningful results)
- Properly installed Bluetooth drivers

### Runtime Dependencies
- `bluetooth_detector.dll` (included with plugin)
- Windows Bluetooth stack
- Visual C++ Runtime (usually pre-installed)

## Troubleshooting

### Common Issues

#### 1. "Failed to load bluetooth_detector.dll"
**Cause**: DLL not found or inaccessible
**Solutions**:
- Ensure `bluetooth_detector.dll` is in the correct directory
- Check Windows permissions
- Verify Visual C++ Runtime is installed

#### 2. "UnsupportedError: Only Windows is supported"
**Cause**: Running on non-Windows platform
**Solution**: This plugin only works on Windows

#### 3. Method returns `false` but Bluetooth works
**Cause**: Driver detection vs. Bluetooth functionality are different
**Explanation**: The method specifically checks driver availability, not current Bluetooth state

### Debugging Tips

```dart
// Enable detailed logging
import 'package:pc_connect/pc_connect.dart';

void debugBluetoothDriver() {
  try {
    print('Attempting to check Bluetooth driver...');
    bool result = BluetoothDriverDetector.isBluetoothDriverWorking();
    print('Driver check result: $result');
  } catch (e) {
    print('Detailed error: $e');
    print('Error type: ${e.runtimeType}');
  }
}
```

## Development

### Building from Source

1. Clone the repository
2. Ensure `bluetooth_detector.dll` is in `windows/runner/`
3. Run the example:
   ```bash
   cd example
   flutter run -d windows
   ```

### Testing

Run the included example application to verify functionality:

```bash
cd example
flutter run -d windows
```

## Contributing

Contributions are welcome! Areas for improvement:
- Additional Windows API integration
- Enhanced error reporting
- Support for more Bluetooth hardware types

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Changelog

### v0.0.1
- Initial release
- Basic Bluetooth driver detection via FFI
- Windows-only support
- Native DLL integration