#include "include/pc_connect/pc_connect_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "pc_connect_plugin.h"

void PcConnectPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  pc_connect::PcConnectPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
