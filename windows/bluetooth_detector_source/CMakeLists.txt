cmake_minimum_required(VERSION 3.14)
project(bluetooth_detector)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 创建DLL
add_library(bluetooth_detector SHARED
    bluetooth_detector.cpp
)

# No additional libraries needed for this simple test

# 设置输出目录
set_target_properties(bluetooth_detector PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/../runner"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_CURRENT_SOURCE_DIR}/../runner"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_CURRENT_SOURCE_DIR}/../runner"
)

# 确保函数正确导出
set_target_properties(bluetooth_detector PROPERTIES
    WINDOWS_EXPORT_ALL_SYMBOLS ON
)
