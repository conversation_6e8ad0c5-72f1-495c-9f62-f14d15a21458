# Bluetooth Detector DLL 构建说明

## 问题描述

当前的 `bluetooth_detector.dll` 没有正确导出 `IsBluetoothDriverWorking` 函数，导致 Dart FFI 无法找到该函数。

## 解决方案

### 方法1：使用提供的源代码重新构建DLL

1. **确保已安装 Visual Studio 2022**
   - 需要包含 C++ 构建工具
   - 需要包含 CMake 支持

2. **构建DLL**
   ```bash
   cd windows/bluetooth_detector_source
   build.bat
   ```

3. **验证构建结果**
   - 检查 `windows/runner/bluetooth_detector.dll` 是否存在
   - 使用 dumpbin 验证函数导出：
   ```bash
   dumpbin /exports windows/runner/bluetooth_detector.dll
   ```

### 方法2：手动使用CMake构建

1. **打开 Visual Studio 开发者命令提示符**

2. **导航到源代码目录**
   ```bash
   cd windows/bluetooth_detector_source
   mkdir build
   cd build
   ```

3. **配置和构建**
   ```bash
   cmake .. -G "Visual Studio 17 2022" -A x64
   cmake --build . --config Release
   ```

4. **复制DLL**
   ```bash
   copy Release\bluetooth_detector.dll ..\..\..\runner\bluetooth_detector.dll
   ```

## 函数导出说明

DLL 必须导出以下函数：

```cpp
extern "C" {
    __declspec(dllexport) bool IsBluetoothDriverWorking();
}
```

## 验证步骤

1. **检查DLL导出**
   ```bash
   dumpbin /exports windows/runner/bluetooth_detector.dll
   ```
   
   应该看到类似输出：
   ```
   ordinal hint RVA      name
         1    0 00001000 IsBluetoothDriverWorking
   ```

2. **测试Flutter应用**
   ```bash
   cd example
   flutter run -d windows
   ```

## 常见问题

### Q: 构建失败，提示找不到 Bthprops.lib
**A:** 确保安装了 Windows SDK，或者移除对蓝牙API的依赖，使用简单的测试实现。

### Q: DLL构建成功但函数仍然找不到
**A:** 检查函数名是否正确导出，确保使用了 `extern "C"` 和 `__declspec(dllexport)`。

### Q: 在不同的构建配置下DLL路径不同
**A:** CMakeLists.txt 已经配置为将DLL输出到正确的位置，确保使用提供的构建脚本。

## 临时解决方案

如果无法重新构建DLL，可以修改Dart代码以返回固定值进行测试：

```dart
static bool isBluetoothDriverWorking() {
  // 临时返回true进行测试
  print('Using fallback implementation');
  return true;
}
```
