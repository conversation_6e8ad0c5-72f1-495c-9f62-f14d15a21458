@echo off
echo Building bluetooth_detector.dll...

REM 创建构建目录
if not exist build mkdir build
cd build

REM 配置CMake项目
cmake .. -G "Visual Studio 17 2022" -A x64

REM 构建项目
cmake --build . --config Release

REM 复制DLL到正确位置
if exist Release\bluetooth_detector.dll (
    copy Release\bluetooth_detector.dll ..\..\..\runner\bluetooth_detector.dll
    echo DLL built and copied successfully!
) else (
    echo Build failed!
    exit /b 1
)

cd ..
echo Build completed!
