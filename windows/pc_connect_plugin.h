#ifndef FLUTTER_PLUGIN_PC_CONNECT_PLUGIN_H_
#define FLUTTER_PLUGIN_PC_CONNECT_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace pc_connect {

class PcConnectPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  PcConnectPlugin();

  virtual ~PcConnectPlugin();

  // Disallow copy and assign.
  PcConnectPlugin(const PcConnectPlugin&) = delete;
  PcConnectPlugin& operator=(const PcConnectPlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace pc_connect

#endif  // FLUTTER_PLUGIN_PC_CONNECT_PLUGIN_H_
