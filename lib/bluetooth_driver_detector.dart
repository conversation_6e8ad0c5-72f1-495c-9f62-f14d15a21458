import 'dart:ffi';
import 'dart:io' show Platform;

// 定义 C++ 函数签名
typedef IsBluetoothDriverWorkingC = Bool Function();
typedef IsBluetoothDriverWorkingDart = bool Function();

class BluetoothDriverDetector {
  static DynamicLibrary? _bluetoothLib;
  static bool _initialized = false;

  static DynamicLibrary? _openBluetoothLibrary() {
    try {
      if (Platform.isWindows) {
        return DynamicLibrary.open('bluetooth_detector.dll');
      } else {
        throw UnsupportedError('Only Windows is supported.');
      }
    } catch (e) {
      print('Failed to load bluetooth_detector.dll: $e');
      return null;
    }
  }

  static bool isBluetoothDriverWorking() {
    try {
      if (!_initialized) {
        _bluetoothLib = _openBluetoothLibrary();
        _initialized = true;
      }

      if (_bluetoothLib == null) {
        print('Bluetooth library not available');
        return false;
      }

      final isWorking = _bluetoothLib!.lookupFunction<
          IsBluetoothDriverWorkingC,
          IsBluetoothDriverWorkingDart>('IsBluetoothDriverWorking');
      return isWorking();
    } catch (e) {
      print('Error calling IsBluetoothDriverWorking: $e');
      return false;
    }
  }
}