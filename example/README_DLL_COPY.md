# DLL 自动拷贝功能

## 概述

为了避免每次更新 `bluetooth_detector.dll` 后需要手动放置到两个路径的麻烦，提供了 PowerShell 脚本来自动拷贝 DLL 文件。

## 使用方法

### 自动拷贝 DLL 文件

在 `example` 目录下执行 PowerShell 脚本：

```powershell
.\copy_dll.ps1
```

### 脚本功能

- 从项目根路径 `../windows/runner/bluetooth_detector.dll` 拷贝到 `example/windows/runner/bluetooth_detector.dll`
- 自动检查源文件是否存在
- 自动创建目标目录（如果不存在）
- 显示拷贝结果和文件信息
- 提供详细的错误信息

### 执行示例

```powershell
PS D:\testcode\pc_connect\example> .\copy_dll.ps1
Copying bluetooth_detector.dll...
Successfully copied bluetooth_detector.dll to: windows/runner/bluetooth_detector.dll
File size: 10240 bytes
Last modified: 07/29/2025 09:42:16
DLL copy completed!
```

## 注意事项

1. 确保在 `example` 目录下执行脚本
2. 确保项目根路径的 `windows/runner/bluetooth_detector.dll` 文件存在
3. 脚本会自动覆盖目标文件（如果存在）

## 开发建议

- 在更新 DLL 文件后，记得运行此脚本
- 可以将此脚本集成到构建流程中
- 建议在运行 Flutter 应用前先执行此脚本 