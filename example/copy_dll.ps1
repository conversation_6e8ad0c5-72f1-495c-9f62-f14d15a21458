# Auto copy bluetooth_detector.dll script
# Copy from project root windows/runner directory to example/windows/runner directory

param(
    [string]$SourcePath = "../windows/runner/bluetooth_detector.dll",
    [string]$DestPath = "windows/runner/bluetooth_detector.dll"
)

Write-Host "Copying bluetooth_detector.dll..." -ForegroundColor Green

# Check if source file exists
if (-not (Test-Path $SourcePath)) {
    Write-Host "Error: Source file not found: $SourcePath" -ForegroundColor Red
    Write-Host "Please ensure bluetooth_detector.dll exists in the project root windows/runner directory" -ForegroundColor Yellow
    exit 1
}

# Check if destination directory exists, create if not
$destDir = Split-Path $DestPath -Parent
if (-not (Test-Path $destDir)) {
    New-Item -ItemType Directory -Path $destDir -Force | Out-Null
    Write-Host "Created directory: $destDir" -ForegroundColor Yellow
}

# Copy file
try {
    Copy-Item -Path $SourcePath -Destination $DestPath -Force
    Write-Host "Successfully copied bluetooth_detector.dll to: $DestPath" -ForegroundColor Green
    
    # Show file information
    $fileInfo = Get-Item $DestPath
    Write-Host "File size: $($fileInfo.Length) bytes" -ForegroundColor Cyan
    Write-Host "Last modified: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan
} catch {
    Write-Host "Copy failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "DLL copy completed!" -ForegroundColor Green 