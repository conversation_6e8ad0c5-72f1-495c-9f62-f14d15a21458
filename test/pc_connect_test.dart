import 'package:flutter_test/flutter_test.dart';
import 'package:pc_connect/pc_connect.dart';
import 'package:pc_connect/pc_connect_platform_interface.dart';
import 'package:pc_connect/pc_connect_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockPcConnectPlatform
    with MockPlatformInterfaceMixin
    implements PcConnectPlatform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final PcConnectPlatform initialPlatform = PcConnectPlatform.instance;

  test('$MethodChannelPcConnect is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelPcConnect>());
  });

  test('getPlatformVersion', () async {
    PcConnect pcConnectPlugin = PcConnect();
    MockPcConnectPlatform fakePlatform = MockPcConnectPlatform();
    PcConnectPlatform.instance = fakePlatform;

    expect(await pcConnectPlugin.getPlatformVersion(), '42');
  });

  test('isBluetoothAvailable returns a bool', () async {
    // 这里只测试接口调用和类型，实际可用性需在 Windows 真机上验证
    final result = await PcConnect.isBluetoothAvailable();
    expect(result, isA<bool>());
  });
}
