import 'package:flutter_test/flutter_test.dart';
import 'package:pc_connect/pc_connect.dart';
import 'package:win_ble/win_ble.dart';

void main() {
  group('PcConnect Bluetooth Tests', () {
    tearDown(() {
      // 清理资源
      PcConnect.dispose();
    });

    test('isBluetoothAvailable should return boolean', () async {
      // 注意：在测试环境中，这个方法可能会抛出异常，因为没有实际的蓝牙硬件
      try {
        final result = await PcConnect.isBluetoothAvailable();
        expect(result, isA<bool>());
      } catch (e) {
        // 在测试环境中，如果没有蓝牙硬件，这是预期的行为
        expect(e, isNotNull);
      }
    });

    test('getBluetoothState should return BleState', () async {
      try {
        final result = await PcConnect.getBluetoothState();
        expect(result, isA<BleState>());
      } catch (e) {
        // 在测试环境中，如果没有蓝牙硬件，这是预期的行为
        expect(e, isNotNull);
      }
    });

    test('isScanning should return false initially', () {
      expect(PcConnect.isScanning, isFalse);
    });

    test('scanResultStream should return a stream', () {
      final stream = PcConnect.scanResultStream;
      expect(stream, isA<Stream<List<BleDevice>>>());
    });

    test('bluetoothStateStream should return a stream', () {
      final stream = PcConnect.bluetoothStateStream;
      expect(stream, isA<Stream<BleState>>());
    });

    test('startScanning should set isScanning to true', () async {
      try {
        await PcConnect.startScanning();
        expect(PcConnect.isScanning, isTrue);
      } catch (e) {
        // 在测试环境中，如果没有蓝牙硬件，这是预期的行为
        expect(e, isNotNull);
      }
    });

    test('stopScanning should set isScanning to false', () async {
      try {
        await PcConnect.startScanning();
        await PcConnect.stopScanning();
        expect(PcConnect.isScanning, isFalse);
      } catch (e) {
        // 在测试环境中，如果没有蓝牙硬件，这是预期的行为
        expect(e, isNotNull);
      }
    });

    test('getConnectedDevices should return a list', () async {
      try {
        final result = await PcConnect.getConnectedDevices();
        expect(result, isA<List<String>>());
      } catch (e) {
        // 在测试环境中，如果没有蓝牙硬件，这是预期的行为
        expect(e, isNotNull);
      }
    });

    test('isDeviceConnected should return false for non-existent device', () async {
      try {
        final result = await PcConnect.isDeviceConnected('00:00:00:00:00:00');
        expect(result, isFalse);
      } catch (e) {
        // 在测试环境中，如果没有蓝牙硬件，这是预期的行为
        expect(e, isNotNull);
      }
    });

    test('connectToDevice should handle invalid address', () async {
      try {
        final result = await PcConnect.connectToDevice('invalid_address');
        expect(result, isFalse);
      } catch (e) {
        // 预期的行为 - 无效地址应该失败
        expect(e, isNotNull);
      }
    });

    test('disconnectFromDevice should handle invalid address', () async {
      try {
        final result = await PcConnect.disconnectFromDevice('invalid_address');
        expect(result, isFalse);
      } catch (e) {
        // 预期的行为 - 无效地址应该失败
        expect(e, isNotNull);
      }
    });

    test('dispose should clean up resources', () {
      // 确保 dispose 方法不抛出异常
      expect(() => PcConnect.dispose(), returnsNormally);
      
      // 调用 dispose 后，isScanning 应该为 false
      expect(PcConnect.isScanning, isFalse);
    });
  });
}